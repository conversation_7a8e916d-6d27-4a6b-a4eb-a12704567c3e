import pandas as pd
import numpy as np
from math import radians, sin, cos, sqrt, atan2

# Load AIS Dynamic Data
dynamic_data = pd.read_csv('Busan_Dynamic_20230601_sorted.csv')

# Define Pilot Boat MMSIs
pilot_mmsi = [
    440111140, 440111150, 440111160, 440008040, 440111190,
    440111210, 440111230, 440110850, 440127410, 440135830,
    440155330, 440105210, 440121630
]

# Detection Parameters
distance_threshold = 0.2 * 1852  # meters
speed_threshold_pilot = 5  # knots
speed_threshold_departure = 8  # knots
speed_threshold_vessel = 10  # knots
duration_threshold = 3 * 60  # seconds (not directly used here, but noted)

# Results container
meeting_events = []

print('Detecting Pilot Transfer Durations...')

# Haversine function
def haversine(lat1, lon1, lat2, lon2):
    R = 6371000  # Earth radius in meters
    phi1, phi2 = radians(lat1), radians(lat2)
    delta_phi = radians(lat2 - lat1)
    delta_lambda = radians(lon2 - lon1)

    a = sin(delta_phi / 2.0)**2 + cos(phi1) * cos(phi2) * sin(delta_lambda / 2.0)**2
    c = 2 * atan2(sqrt(a), sqrt(1 - a))

    return R * c

# Convert time column to datetime if necessary
if not np.issubdtype(dynamic_data['time'].dtype, np.datetime64):
    dynamic_data['time'] = pd.to_datetime(dynamic_data['time'])

# Main loop
for i, row_i in dynamic_data.iterrows():
    if row_i['MMSI'] in pilot_mmsi:
        MMSI1 = row_i['MMSI']
        time1 = row_i['time']
        lat1 = row_i['Latitude']
        lon1 = row_i['Longitude']
        sog1 = row_i['SOG']

        same_time_rows = dynamic_data[dynamic_data['time'] == time1]

        for _, row_j in same_time_rows.iterrows():
            MMSI2 = row_j['MMSI']
            if MMSI2 != MMSI1:
                lat2 = row_j['Latitude']
                lon2 = row_j['Longitude']
                sog2 = row_j['SOG']

                distance = haversine(lat1, lon1, lat2, lon2)

                if (distance <= distance_threshold and
                    sog1 <= speed_threshold_pilot and
                    sog2 <= speed_threshold_vessel):

                    start_time = time1
                    start_lat = lat1
                    start_lon = lon1

                    meeting_duration = 0
                    end_time = None

                    pilot_data = dynamic_data[(dynamic_data['MMSI'] == MMSI1) & 
                                              (dynamic_data['time'] >= time1)]

                    for _, row_k in pilot_data.iterrows():
                        current_time = row_k['time']
                        current_lat = row_k['Latitude']
                        current_lon = row_k['Longitude']
                        current_sog = row_k['SOG']

                        current_distance = haversine(start_lat, start_lon, current_lat, current_lon)

                        if current_distance > distance_threshold or current_sog > speed_threshold_departure:
                            end_time = current_time
                            break
                        else:
                            meeting_duration += 1

                    if end_time:
                        meeting_events.append({
                            'startTime': start_time,
                            'MMSI1': MMSI1,
                            'startLat': start_lat,
                            'startLon': start_lon,
                            'MMSI2': MMSI2,
                            'initialDistance': distance,
                            'endTime': end_time,
                            'meetingDuration': meeting_duration
                        })

# Save results to CSV
meeting_df = pd.DataFrame(meeting_events)
meeting_df.to_csv('pilot_transfer_durations.csv', index=False)

print('Pilot Transfer Durations Detected and Saved.')
