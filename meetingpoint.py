# -*- coding: utf-8 -*-
"""meetingPoint.ipynb

Automatically generated by <PERSON>b.

Original file is located at
    https://colab.research.google.com/drive/1MgA6q4GjvWcbNLksMupX659YA55OfBj6
"""

import numpy as np
import matplotlib.pyplot as plt

# Example coordinates for the two vessels and the meeting point
vessel1_lat, vessel1_lon = 35.00, 128.85
vessel2_lat, vessel2_lon = 35.10, 128.95
meeting_lat, meeting_lon = 35.05, 128.9

# Plot the vessels and the meeting point with the safety ring
plt.figure(figsize=(10, 8))

# Plot vessels
plt.scatter(vessel1_lon, vessel1_lat, c='blue', s=100, label='Vessel 1', alpha=0.7)
plt.scatter(vessel2_lon, vessel2_lat, c='red', s=100, label='Vessel 2', alpha=0.7)

# Plot meeting point
plt.scatter(meeting_lon, meeting_lat, c='green', s=150, label='Meeting Point')

# Plot safety ring (0.2 nm radius)
radius_nm = 0.2  # Safety radius in nautical miles
radius_km = radius_nm * 1.852  # Convert to kilometers

# Create a circle around the meeting point
theta = np.linspace(0, 2 * np.pi, 100)
lat_circle = meeting_lat + (radius_km / 111) * np.cos(theta)  # Approximate latitude change
lon_circle = meeting_lon + (radius_km / (111 * np.cos(np.radians(meeting_lat)))) * np.sin(theta)  # Approximate longitude change

plt.plot(lon_circle, lat_circle, 'g--', label='Safety Zone (0.2 nm)', alpha=0.6)

# Plot routes from vessels to the meeting point
plt.plot([vessel1_lon, meeting_lon], [vessel1_lat, meeting_lat], 'b-', label='Vessel 1 Route')
plt.plot([vessel2_lon, meeting_lon], [vessel2_lat, meeting_lat], 'r-', label='Vessel 2 Route')

# Add labels and grid
plt.title('Vessels Heading to Meeting Point (Safety Zone)')
plt.xlabel('Longitude')
plt.ylabel('Latitude')
plt.grid(True)
plt.legend()

# Show the plot
plt.show()

import numpy as np
import matplotlib.pyplot as plt

# Example coordinates for the two vessels and the meeting point
vessel1_lat, vessel1_lon = 35.00, 128.85
vessel2_lat, vessel2_lon = 35.10, 128.95
meeting_lat, meeting_lon = 35.05, 128.9

# Generate random traffic (vessels) around the area
num_vessels = 10  # Number of random vessels to plot
traffic_lat = meeting_lat + np.random.uniform(-0.05, 0.05, num_vessels)  # Random latitudes around meeting point
traffic_lon = meeting_lon + np.random.uniform(-0.05, 0.05, num_vessels)  # Random longitudes around meeting point

# Plot the vessels and the meeting point with the safety ring
plt.figure(figsize=(10, 8))

# Plot vessels
plt.scatter(vessel1_lon, vessel1_lat, c='blue', s=100, label='Vessel 1', alpha=0.7)
plt.scatter(vessel2_lon, vessel2_lat, c='red', s=100, label='Vessel 2', alpha=0.7)

# Plot meeting point
plt.scatter(meeting_lon, meeting_lat, c='green', s=150, label='Meeting Point')

# Plot safety ring (0.2 nm radius)
radius_nm = 0.2  # Safety radius in nautical miles
radius_km = radius_nm * 1.852  # Convert to kilometers

# Create a circle around the meeting point
theta = np.linspace(0, 2 * np.pi, 100)
lat_circle = meeting_lat + (radius_km / 111) * np.cos(theta)  # Approximate latitude change
lon_circle = meeting_lon + (radius_km / (111 * np.cos(np.radians(meeting_lat)))) * np.sin(theta)  # Approximate longitude change

plt.plot(lon_circle, lat_circle, 'g--', label='Safety Zone (0.2 nm)', alpha=0.6)

# Plot routes from vessels to the meeting point
plt.plot([vessel1_lon, meeting_lon], [vessel1_lat, meeting_lat], 'b-', label='Vessel 1 Route')
plt.plot([vessel2_lon, meeting_lon], [vessel2_lat, meeting_lat], 'r-', label='Vessel 2 Route')

# Plot random traffic
plt.scatter(traffic_lon, traffic_lat, c='gray', s=80, label='Other Traffic', alpha=0.6)

# Add labels and grid
plt.title('Vessels Heading to Meeting Point (Safety Zone) with Random Traffic')
plt.xlabel('Longitude')
plt.ylabel('Latitude')
plt.grid(True)
plt.legend()

# Show the plot
plt.show()

limport numpy as np
import matplotlib.pyplot as plt

# Haversine function to calculate distance between two lat-long points
def haversine(lat1, lon1, lat2, lon2):
    R = 6371  # Earth radius in kilometers
    lat1, lon1, lat2, lon2 = map(np.radians, [lat1, lon1, lat2, lon2])
    dlat = lat2 - lat1
    dlon = lon2 - lon1
    a = np.sin(dlat / 2)**2 + np.cos(lat1) * np.cos(lat2) * np.sin(dlon / 2)**2
    c = 2 * np.arctan2(np.sqrt(a), np.sqrt(1 - a))
    return R * c  # Distance in kilometers

# Function to generate a new meeting point if necessary
def adjust_meeting_point(meeting_lat, meeting_lon, traffic_lat, traffic_lon, safety_radius_km):
    for lat, lon in zip(traffic_lat, traffic_lon):
        dist_to_traffic = haversine(meeting_lat, meeting_lon, lat, lon)
        if dist_to_traffic < safety_radius_km:
            # If any traffic vessel is too close to the meeting point, move the meeting point
            print(f"Traffic too close to meeting point (Distance: {dist_to_traffic:.2f} km). Adjusting...")
            # Move the meeting point slightly away by a random small offset
            meeting_lat += np.random.uniform(-0.02, 0.02)
            meeting_lon += np.random.uniform(-0.02, 0.02)
            return adjust_meeting_point(meeting_lat, meeting_lon, traffic_lat, traffic_lon, safety_radius_km)
    return meeting_lat, meeting_lon

# Example coordinates for the two vessels and the meeting point
vessel1_lat, vessel1_lon = 35.00, 128.85
vessel2_lat, vessel2_lon = 35.10, 128.95
initial_meeting_lat, initial_meeting_lon = 35.05, 128.9

# Generate random traffic (vessels) around the area
num_vessels = 10  # Number of random vessels to plot
traffic_lat = initial_meeting_lat + np.random.uniform(-0.05, 0.05, num_vessels)  # Random latitudes around meeting point
traffic_lon = initial_meeting_lon + np.random.uniform(-0.05, 0.05, num_vessels)  # Random longitudes around meeting point

# Safety radius in kilometers (0.2 nautical miles converted to km)
safety_radius_nm = 0.2
safety_radius_km = safety_radius_nm * 1.852

# Adjust meeting point if any traffic is within the safety radius
meeting_lat, meeting_lon = adjust_meeting_point(initial_meeting_lat, initial_meeting_lon, traffic_lat, traffic_lon, safety_radius_km)

# Plot the vessels, meeting point, safety ring, and random traffic
plt.figure(figsize=(10, 8))

# Plot vessels
plt.scatter(vessel1_lon, vessel1_lat, c='blue', s=100, label='Vessel 1', alpha=0.7)
plt.scatter(vessel2_lon, vessel2_lat, c='red', s=100, label='Vessel 2', alpha=0.7)

# Plot meeting point
plt.scatter(meeting_lon, meeting_lat, c='green', s=150, label='Meeting Point')

# Plot safety ring (0.2 nm radius)
theta = np.linspace(0, 2 * np.pi, 100)
lat_circle = meeting_lat + (safety_radius_km / 111) * np.cos(theta)  # Approximate latitude change
lon_circle = meeting_lon + (safety_radius_km / (111 * np.cos(np.radians(meeting_lat)))) * np.sin(theta)  # Approximate longitude change
plt.plot(lon_circle, lat_circle, 'g--', label='Safety Zone (0.2 nm)', alpha=0.6)

# Plot routes from vessels to the meeting point
plt.plot([vessel1_lon, meeting_lon], [vessel1_lat, meeting_lat], 'b-', label='Vessel 1 Route')
plt.plot([vessel2_lon, meeting_lon], [vessel2_lat, meeting_lat], 'r-', label='Vessel 2 Route')

# Plot random traffic
plt.scatter(traffic_lon, traffic_lat, c='gray', s=80, label='Other Traffic', alpha=0.6)

# Add labels and grid
plt.title('Vessels Heading to Adjusted Meeting Point (Safety Zone) with Traffic')
plt.xlabel('Longitude')
plt.ylabel('Latitude')
plt.grid(True)
plt.legend()

# Show the plot
plt.show()

!pip install PyKrige

# First, install PyKrige
!pip install PyKrige

import numpy as np
import matplotlib.pyplot as plt
from pykrige.ok import OrdinaryKriging

# Haversine function to calculate the distance between two lat-long points
def haversine(lat1, lon1, lat2, lon2):
    R = 6371  # Earth radius in kilometers
    lat1, lon1, lat2, lon2 = map(np.radians, [lat1, lon1, lat2, lon2])
    dlat = lat2 - lat1
    dlon = lon2 - lon1
    a = np.sin(dlat / 2)**2 + np.cos(lat1) * np.cos(lat2) * np.sin(dlon / 2)**2
    c = 2 * np.arctan2(np.sqrt(a), np.sqrt(1 - a))
    return R * c  # Distance in kilometers

# Generate waypoints with safety adjustments
def generate_safe_route(start_lat, start_lon, end_lat, end_lon, traffic_lat, traffic_lon, num_waypoints=5, safety_distance=0.5):
    waypoints_lat = np.linspace(start_lat, end_lat, num_waypoints + 2)
    waypoints_lon = np.linspace(start_lon, end_lon, num_waypoints + 2)

    # Adjust waypoints to avoid traffic vessels
    for i in range(1, len(waypoints_lat) - 1):
        for t_lat, t_lon in zip(traffic_lat, traffic_lon):
            dist_to_traffic = haversine(waypoints_lat[i], waypoints_lon[i], t_lat, t_lon)
            if dist_to_traffic < safety_distance:
                # If waypoint is too close to traffic, adjust it by a small offset
                waypoints_lat[i] += np.random.uniform(-0.01, 0.01)
                waypoints_lon[i] += np.random.uniform(-0.01, 0.01)

    return waypoints_lat, waypoints_lon

# Function to adjust the meeting point if it is too close to risk or traffic
def adjust_meeting_point(lat, lon, risk_map, traffic_lat, traffic_lon, grid_lat, grid_lon, threshold=0.5, safety_distance=0.5):
    while True:
        min_risk = risk_map[np.unravel_index(np.argmin(risk_map, axis=None), risk_map.shape)]
        for t_lat, t_lon in zip(traffic_lat, traffic_lon):
            dist_to_traffic = haversine(lat, lon, t_lat, t_lon)
            if dist_to_traffic < safety_distance:
                print(f"Traffic too close to the meeting point (Distance: {dist_to_traffic:.2f} km). Adjusting...")
                lat += np.random.uniform(-0.01, 0.01)
                lon += np.random.uniform(-0.01, 0.01)
                continue

        if min_risk > threshold:
            print("Risk too high at this location. Adjusting...")
            lat += np.random.uniform(-0.01, 0.01)
            lon += np.random.uniform(-0.01, 0.01)
        else:
            break
    return lat, lon

# Example grid of latitudes and longitudes (potential meeting points)
grid_lat = np.linspace(34.95, 35.15, 50)
grid_lon = np.linspace(128.8, 129.0, 50)
grid_lon, grid_lat = np.meshgrid(grid_lon, grid_lat)

# Observations of external factors at specific points (e.g., traffic density, weather risk, etc.)
observed_lats = np.array([35.00, 35.05, 35.10])  # Latitudes of observation points
observed_lons = np.array([128.85, 128.90, 128.95])  # Longitudes of observation points
observed_risks = np.array([0.7, 0.3, 0.5])  # Risk values: higher = more dangerous (based on external factors)

# Apply Ordinary Kriging for spatial interpolation
OK = OrdinaryKriging(observed_lons, observed_lats, observed_risks, variogram_model='linear',
                     verbose=False, enable_plotting=False)

# Interpolate risk across the entire grid
z, ss = OK.execute('grid', grid_lon[0, :], grid_lat[:, 0])

# Reshape z to match the grid shape
z = z.reshape(grid_lat.shape)

# Generate a random initial meeting point near the center of the canvas
initial_lat = np.random.uniform(35.00, 35.10)
initial_lon = np.random.uniform(128.85, 128.95)

# Define random vessel traffic in the region
num_traffic_vessels = 8  # Number of random vessels
traffic_lat = np.random.uniform(34.96, 35.14, num_traffic_vessels)
traffic_lon = np.random.uniform(128.82, 128.98, num_traffic_vessels)

# Dynamically adjust the meeting point if the risk is too high or traffic is too close
adjusted_lat, adjusted_lon = adjust_meeting_point(initial_lat, initial_lon, z, traffic_lat, traffic_lon, grid_lat, grid_lon)

# Define the ship and pilot boat positions
ship_lat, ship_lon = 35.00, 128.85  # Ship's starting position
pilot_lat, pilot_lon = 35.10, 128.95  # Pilot boat's starting position

# Generate safe routes with multiple waypoints
ship_route_lat, ship_route_lon = generate_safe_route(ship_lat, ship_lon, adjusted_lat, adjusted_lon, traffic_lat, traffic_lon)
pilot_route_lat, pilot_route_lon = generate_safe_route(pilot_lat, pilot_lon, adjusted_lat, adjusted_lon, traffic_lat, traffic_lon)

# Visualize the interpolated risk map and the (adjusted) optimal meeting point
plt.contourf(grid_lon, grid_lat, z, cmap='RdYlGn_r', levels=20)
plt.colorbar(label='Risk Level')

# Plot the ship and pilot boat as triangles
plt.scatter(ship_lon, ship_lat, marker=(3, 0, 0), color='blue', s=200, label='Ship', zorder=10)  # Triangle facing north
plt.scatter(pilot_lon, pilot_lat, marker=(3, 0, 180), color='red', s=200, label='Pilot Boat', zorder=10)  # Triangle facing south

# Plot the adjusted meeting point
plt.scatter(adjusted_lon, adjusted_lat, c='green', s=150, label='Adjusted Meeting Point', zorder=10)

# Plot random traffic
plt.scatter(traffic_lon, traffic_lat, c='gray', s=80, label='Random Traffic', zorder=8)

# Plot safe route for the ship and pilot boat
plt.plot(ship_route_lon, ship_route_lat, 'b--', label='Ship Route', zorder=5)
plt.plot(pilot_route_lon, pilot_route_lat, 'r--', label='Pilot Boat Route', zorder=5)

# Plot a safety ring (0.2 nm radius) around the adjusted meeting point
radius_km = 0.2 * 1.852  # Convert 0.2 nm to kilometers
theta = np.linspace(0, 2 * np.pi, 100)
lat_circle = adjusted_lat + (radius_km / 111) * np.cos(theta)  # Latitude adjustment for circle
lon_circle = adjusted_lon + (radius_km / (111 * np.cos(np.radians(adjusted_lat)))) * np.sin(theta)  # Longitude adjustment for circle
plt.plot(lon_circle, lat_circle, 'g--', label='Safety Zone (0.2 nm)', alpha=0.6)

# Label the adjusted meeting point
plt.annotate('Meeting Point', xy=(adjusted_lon, adjusted_lat), xytext=(adjusted_lon+0.005, adjusted_lat-0.005),
             arrowprops=dict(facecolor='green', shrink=0.05), fontsize=12)

# Plot title and axis labels
plt.title('Vessels Heading to Adjusted Meeting Point (Safety Zone) with Random Traffic and Safe Routes')
plt.xlabel('Longitude')
plt.ylabel('Latitude')
plt.legend()
plt.grid(True)
plt.show()

# Output the adjusted meeting point
print(f"Adjusted meeting point (lat, lon): ({adjusted_lat:.6f}, {adjusted_lon:.6f})")

