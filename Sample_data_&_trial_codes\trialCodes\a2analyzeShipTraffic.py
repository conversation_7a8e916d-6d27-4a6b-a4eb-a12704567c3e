# Import necessary libraries
import pandas as pd
import numpy as np

# Read data from the CSV files
dynamicData = pd.read_csv('Busan_Dynamic_XY.csv')  # Dynamic data
staticData = pd.read_csv('Static_Busan_Dynamic_20230607.csv')  # Static data

# Extract relevant columns from dynamic data
DateTime = dynamicData['DateTime']
MMSI = dynamicData['MMSI']
Latitude = dynamicData['Latitude']
Longitude = dynamicData['Longitude']
COG = dynamicData['COG']
SOG = dynamicData['SOG']
xCart = dynamicData['X_Cartesian']
yCart = dynamicData['Y_Cartesian']

# Extract relevant columns from static data
staticMMSI = staticData['MMSI']
shipType = staticData['ShipType']
LOA = staticData['LOA']
width = staticData['Width']
draft = staticData['Draft']

numVessels = len(dynamicData)
crossTrafficData = []

print('Calculating Cross-Traffic...')
import time
start_time = time.time()

for o in range(numVessels):
    for t in range(numVessels):
        if (MMSI[o] != MMSI[t]) and (DateTime[o] == DateTime[t]):
            # Use Cartesian coordinates for distance calculation
            dx = xCart[t] - xCart[o]
            dy = yCart[t] - yCart[o]
            distance = np.sqrt(dx**2 + dy**2)  # Distance in meters
            
            if distance <= 3704:
                deltaCOG = abs(COG[o] - COG[t])
                deltaCOG = min(deltaCOG, 360 - deltaCOG)

                U2 = np.degrees(np.arctan2(dy, dx))
                Rel_B = (U2 - COG[o] + 360) % 360
                
                isCrossTraffic = (deltaCOG >= 45 and deltaCOG <= 135)

                # Get static data for the first vessel
                staticIndex1 = np.where(staticMMSI == MMSI[o])[0]
                if staticIndex1.size > 0:
                    shipType1 = shipType[staticIndex1[0]]
                    LOA1 = LOA[staticIndex1[0]]
                    width1 = width[staticIndex1[0]]
                    draft1 = draft[staticIndex1[0]]
                else:
                    shipType1 = np.nan
                    LOA1 = np.nan
                    width1 = np.nan
                    draft1 = np.nan
                
                # Get static data for the second vessel
                staticIndex2 = np.where(staticMMSI == MMSI[t])[0]
                if staticIndex2.size > 0:
                    shipType2 = shipType[staticIndex2[0]]
                    LOA2 = LOA[staticIndex2[0]]
                    width2 = width[staticIndex2[0]]
                    draft2 = draft[staticIndex2[0]]
                else:
                    shipType2 = np.nan
                    LOA2 = np.nan
                    width2 = np.nan
                    draft2 = np.nan
                
                distance = round(distance)
                Rel_B = round(Rel_B)
                
                # Calculate CPA
                dcpa, tcpa, cpa_lat, cpa_lon, cpa_distance = calculateCPA(
                    Latitude[o], Longitude[o], COG[o], SOG[o],
                    Latitude[t], Longitude[t], COG[t], SOG[t]
                )
                
                dcpa = round(dcpa)
                tcpa = round(tcpa, 3)
                cpa_lat = round(cpa_lat, 6)
                cpa_lon = round(cpa_lon, 6)
                
                crossTrafficData.append([
                    DateTime[o], MMSI[o], Latitude[o], Longitude[o], xCart[o], yCart[o],
                    MMSI[t], Latitude[t], Longitude[t], xCart[t], yCart[t],
                    distance, deltaCOG, Rel_B,
                    shipType1, LOA1, width1, draft1,
                    shipType2, LOA2, width2, draft2,
                    isCrossTraffic, dcpa, tcpa, cpa_lat, cpa_lon, cpa_distance
                ])

# Convert crossTrafficData to DataFrame
crossTrafficData = pd.DataFrame(crossTrafficData, columns=[
    'Time', 'MMSI1', 'Latitude1', 'Longitude1', 'X1', 'Y1',
    'MMSI2', 'Latitude2', 'Longitude2', 'X2', 'Y2',
    'Distance (m)', 'DeltaCOG (degrees)', 'Relative Bearing (degrees)',
    'ShipType1', 'LOA1', 'Width1', 'Draft1',
    'ShipType2', 'LOA2', 'Width2', 'Draft2',
    'CrossTraffic', 'DCPA (m)', 'TCPA (s)', 'CPA_Latitude', 'CPA_Longitude', 'CPA_Distance (m)'
])

# Save results to CSV
crossTrafficData.to_csv('cross_traffic_results.csv', index=False)

print('Cross-Traffic Calculations completed and results saved to CSV.')
print(f"Execution time: {time.time() - start_time:.2f} seconds")

def calculateCPA(lat1, lon1, cog1, sog1, lat2, lon2, cog2, sog2):
    R = 6371000  # Radius of the Earth in meters
    x1 = R * np.cos(np.radians(lat1)) * np.cos(np.radians(lon1))
    y1 = R * np.cos(np.radians(lat1)) * np.sin(np.radians(lon1))
    x2 = R * np.cos(np.radians(lat2)) * np.cos(np.radians(lon2))
    y2 = R * np.cos(np.radians(lat2)) * np.sin(np.radians(lon2))

    vx1 = sog1 * np.sin(np.radians(cog1))
    vy1 = sog1 * np.cos(np.radians(cog1))
    vx2 = sog2 * np.sin(np.radians(cog2))
    vy2 = sog2 * np.cos(np.radians(cog2))

    vx = vx2 - vx1
    vy = vy2 - vy1

    dx = x2 - x1
    dy = y2 - y1

    denominator = (vx**2 + vy**2)
    if denominator == 0:
        tcpa = 0  # or some other appropriate value
    else:
        tcpa = -(dx * vx + dy * vy) / denominator

    cpa_x = x1 + vx1 * tcpa
    cpa_y = y1 + vy1 * tcpa

    cpa_lon = np.degrees(np.arctan2(cpa_y, cpa_x))
    cpa_lat = np.degrees(np.arcsin(max(min(cpa_y / R, 1), -1)))  # Clamp value to [-1, 1]

    dcpa = np.sqrt((x1 + vx1 * tcpa - x2 - vx2 * tcpa)**2 + (y1 + vy1 * tcpa - y2 - vy2 * tcpa)**2)

    tcpa = tcpa * 3600  # Convert to seconds
    
    # Calculate CPA distance
    cpa_distance = round(np.sqrt((x1 + vx1 * tcpa - x2 - vx2 * tcpa)**2 + (y1 + vy1 * tcpa - y2 - vy2 * tcpa)**2))
    
    return dcpa, tcpa, cpa_lat, cpa_lon, cpa_distance
