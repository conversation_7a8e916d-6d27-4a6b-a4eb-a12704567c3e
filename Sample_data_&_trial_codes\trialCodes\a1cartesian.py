# Import necessary libraries
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt

# Read data from the CSV file
dynamicData = pd.read_csv('Busan_Dynamic_20230607_sorted.csv')

# Extract relevant columns
Latitude = dynamicData['Latitude']
Longitude = dynamicData['Longitude']
MMSI = dynamicData['MMSI']
DateTime = dynamicData['DateTime']

# UTM Conversion Constants
a = 6378137  # Semi-major axis of the Earth (WGS84)
f = 1 / 298.257223563  # Flattening (WGS84)
k0 = 0.9996  # Scale factor
e2 = 2 * f - f**2  # Square of eccentricity
zone = 52  # UTM Zone for Busan
lon0 = (zone - 1) * 6 - 180 + 3  # Central meridian of the zone

# Convert Latitude and Longitude to UTM
xCart = np.zeros(len(Latitude))
yCart = np.zeros(len(Latitude))

for i in range(len(Latitude)):
    lat = np.radians(Latitude[i])
    lon = np.radians(Longitude[i])
    
    N = a / np.sqrt(1 - e2 * np.sin(lat)**2)  # Radius of curvature
    T = np.tan(lat)**2
    C = e2 / (1 - e2) * np.cos(lat)**2
    A = np.cos(lat) * (lon - np.radians(lon0))
    
    M = a * ((1 - e2 / 4 - 3 * e2**2 / 64 - 5 * e2**3 / 256) * lat -
              (3 * e2 / 8 + 3 * e2**2 / 32 + 45 * e2**3 / 1024) * np.sin(2 * lat) +
              (15 * e2**2 / 256 + 45 * e2**3 / 1024) * np.sin(4 * lat) -
              (35 * e2**3 / 3072) * np.sin(6 * lat))
    
    xCart[i] = k0 * N * (A + (1 - T + C) * A**3 / 6 + (5 - 18 * T + T**2 + 72 * C - 58 * e2) * A**5 / 120)
    yCart[i] = k0 * (M + N * np.tan(lat) * (A**2 / 2 + (5 - T + 9 * C + 4 * C**2) * A**4 / 24 +
              (61 - 58 * T + T**2 + 600 * C - 330 * e2) * A**6 / 720))

# Add Cartesian coordinates to the dynamic data table
dynamicData['X_Cartesian'] = xCart
dynamicData['Y_Cartesian'] = yCart

# Save the results to a new CSV file
dynamicData.to_csv('Busan_Dynamic_XY.csv', index=False)

# Plot the ship positions
plt.figure()
plt.scatter(xCart, yCart, s=10, c='blue', alpha=0.5)
plt.title('Ship Positions in Cartesian Coordinates')
plt.xlabel('X (meters)')
plt.ylabel('Y (meters)')
plt.grid(True)

# Annotate the plot with MMSI if needed
for i in range(min(10, len(MMSI))):  # Limit annotations to the first 10 points to avoid clutter
    plt.text(xCart[i], yCart[i], str(MMSI[i]), fontsize=8, ha='right')

plt.show()

print('Conversion completed and results saved as Busan_Dynamic_XY.csv')
