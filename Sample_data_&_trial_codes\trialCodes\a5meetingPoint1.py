# Import necessary libraries
import pandas as pd
import numpy as np
from netCDF4 import Dataset

def add_bathymetry_data(csv_file='cross_traffic_results_with_environmental_data.csv'):
    # Default file names
    bathymetry_file = 'gebco_2024_n35.149_s34.691_w128.6523_e129.0327.nc'
    tidal_file = 'gebco_2024_tid_n35.149_s34.691_w128.6523_e129.0327.nc'
    output_file = 'updated_cross_traffic_results.csv'

    # Check if input files exist
    try:
        data = pd.read_csv(csv_file)
    except FileNotFoundError:
        raise FileNotFoundError(f'CSV file not found: {csv_file}')
    
    # Latitude and longitude columns
    lat_col = 'Latitude1'  # Adjust based on your CSV structure
    lon_col = 'Longitude1'  # Adjust based on your CSV structure

    if lat_col not in data.columns or lon_col not in data.columns:
        raise ValueError('Latitude or Longitude column not found in CSV file.')

    csv_lat = data[lat_col].values
    csv_lon = data[lon_col].values

    # Read bathymetry data
    bathy_depth = read_netcdf_depth(bathymetry_file, 'elevation')

    # Read tidal data
    tidal_depth = read_netcdf_depth(tidal_file, 'tid')

    # Check if CSV lat/lon are within bounds of bathymetry data
    if any(csv_lat < min(bathy_depth['lat'])) or any(csv_lat > max(bathy_depth['lat'])) or \
       any(csv_lon < min(bathy_depth['lon'])) or any(csv_lon > max(bathy_depth['lon'])):
        raise ValueError('Some GPS coordinates from the CSV are outside the bounds of the bathymetry data.')

    # Find nearest depths
    bathy_depths = find_nearest_depths(csv_lat, csv_lon, bathy_depth)
    tidal_depths = find_nearest_depths(csv_lat, csv_lon, tidal_depth)

    # Add new columns
    data['BathymetryDepth'] = bathy_depths
    data['TidalDepth'] = tidal_depths
    data['TotalDepth'] = bathy_depths + tidal_depths

    # Save updated data
    data.to_csv(output_file, index=False)
    print(f'Processing complete. Data saved to {output_file}')

def read_netcdf_depth(file_path, depth_var_name):
    # Open NetCDF file
    with Dataset(file_path, 'r') as nc:
        lat = nc.variables['lat'][:]
        lon = nc.variables['lon'][:]
        depth = nc.variables[depth_var_name][:]
    
    return {'lat': lat, 'lon': lon, 'depth': depth}

def find_nearest_depths(csv_lat, csv_lon, depth_data):
    # Preallocate result
    nearest_depths = np.zeros(len(csv_lat))
    
    # Create meshgrid
    lon_grid, lat_grid = np.meshgrid(depth_data['lon'], depth_data['lat'])
    
    # Find nearest point
    for i in range(len(csv_lat)):
        # Calculate distances
        distances = (lat_grid - csv_lat[i])**2 + (lon_grid - csv_lon[i])**2
        idx = np.argmin(distances)
        row, col = np.unravel_index(idx, distances.shape)

        # Assign nearest depth
        nearest_depths[i] = depth_data['depth'][row, col]
    
    return nearest_depths

# Run the function to add bathymetry data
add_bathymetry_data()
