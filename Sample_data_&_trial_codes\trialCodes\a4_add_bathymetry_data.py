# Import necessary libraries
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt

# Read the cross traffic results CSV file
cross_traffic_data = pd.read_csv('cross_traffic_results.csv')

# Read the hourly tide heights CSV file
tide_data = pd.read_csv('hourly_tide_heights.csv')

# Find the column index for the time data in cross_traffic_data
time_column_index = cross_traffic_data.columns.get_loc('Time')

# Convert time numbers to hours (including fractional hours)
cross_traffic_time = cross_traffic_data.iloc[:, time_column_index]
cross_traffic_hours = (cross_traffic_time // 10000) + \
                      ((cross_traffic_time // 100) % 100) / 60 + \
                      (cross_traffic_time % 100) / 3600

tide_time = tide_data['time']
tide_hours = (tide_time // 10000) + ((tide_time // 100) % 100) / 60

# Interpolate tide heights for each entry in cross traffic data
interpolated_heights = np.interp(cross_traffic_hours, tide_hours, tide_data['height'])

# Add interpolated tide heights as a new column
cross_traffic_data['TideHeight'] = interpolated_heights

num_rows = len(cross_traffic_data)

# Generate random current velocity (in knots, between 0 and 3 knots)
current_velocity = 3 * np.random.rand(num_rows)

# Generate random current direction (in degrees, between 0 and 360)
current_direction = 360 * np.random.rand(num_rows)

# Generate random wind speed (in knots, between 0 and 20 knots)
wind_speed = 20 * np.random.rand(num_rows)

# Generate random wind direction (in degrees, between 0 and 360)
wind_direction = 360 * np.random.rand(num_rows)

# Add the new columns to the dataset
cross_traffic_data['CurrentVelocity'] = current_velocity
cross_traffic_data['CurrentDirection'] = current_direction
cross_traffic_data['WindSpeed'] = wind_speed
cross_traffic_data['WindDirection'] = wind_direction

# Save the updated data to a new CSV file
cross_traffic_data.to_csv('cross_traffic_results_with_environmental_data.csv', index=False)

# Display confirmation message and statistics
print('Process completed. Updated data saved to cross_traffic_results_with_environmental_data.csv')
print(f'Total rows in cross_traffic_data: {len(cross_traffic_data)}')
print(f'Range of interpolated tide heights: {min(interpolated_heights)} to {max(interpolated_heights)}')
print(f'Range of current velocities: {min(current_velocity)} to {max(current_velocity)} knots')
print(f'Range of current directions: {min(current_direction)} to {max(current_direction)} degrees')
print(f'Range of wind speeds: {min(wind_speed)} to {max(wind_speed)} knots')
print(f'Range of wind directions: {min(wind_direction)} to {max(wind_direction)} degrees')

# Optional: Create a scatter plot of wind data
plt.figure()
plt.scatter(wind_direction, wind_speed, s=10, c='blue', alpha=0.5)
plt.title('Wind Speed vs Direction')
plt.xlabel('Wind Direction (degrees)')
plt.ylabel('Wind Speed (knots)')
plt.grid(True)
plt.show()
